import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:flutter/material.dart';

/// {@template face_guide_overlay}
/// Widget that displays a face guide overlay with real-time detection feedback.
///
/// Shows an oval guide for face positioning and changes color based on
/// face detection quality and coverage.
/// {@endtemplate}
class FaceGuideOverlay extends StatefulWidget {
  /// {@macro face_guide_overlay}
  const FaceGuideOverlay({
    super.key,
    this.currentDetection,
    this.isRecording = false,
  });

  /// Current face detection result
  final FaceDetectionResult? currentDetection;

  /// Whether recording is in progress
  final bool isRecording;

  @override
  State<FaceGuideOverlay> createState() => _FaceGuideOverlayState();
}

class _FaceGuideOverlayState extends State<FaceGuideOverlay>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  // Color animation for smooth transitions
  late AnimationController _colorController;
  late Animation<Color?> _colorAnimation;

  Color _currentGuideColor = Colors.white;

  @override
  void initState() {
    super.initState();

    // Subtle pulse animation for guide (slower and less prominent)
    _pulseController = AnimationController(
      duration: const Duration(seconds: 4), // Slower pulse
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.95, // Less dramatic scaling
      end: 1,
    ).animate(
      CurvedAnimation(
        parent: _pulseController,
        curve: Curves.easeInOut,
      ),
    );

    // Color transition animation for smooth feedback
    _colorController = AnimationController(
      duration: const Duration(milliseconds: 500), // Smooth color transitions
      vsync: this,
    );

    _updateColorAnimation();
    _pulseController.repeat(reverse: true);
  }

  @override
  void didUpdateWidget(FaceGuideOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Only update color if detection status meaningfully changed
    final oldColor = _getGuideColor(oldWidget.currentDetection);
    final newColor = _getGuideColor(widget.currentDetection);

    if (oldColor != newColor) {
      _updateColorAnimation();
      _colorController.forward();
    }
  }

  void _updateColorAnimation() {
    final targetColor = _getGuideColor(widget.currentDetection);
    _colorAnimation = ColorTween(
      begin: _currentGuideColor,
      end: targetColor,
    ).animate(
      CurvedAnimation(
        parent: _colorController,
        curve: Curves.easeInOut,
      ),
    );

    _colorAnimation.addListener(() {
      if (mounted) {
        setState(() {
          _currentGuideColor = _colorAnimation.value ?? Colors.white;
        });
      }
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _colorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Stack(
          children: [
            // Dark overlay with cutout and face guide (combined)
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return CustomPaint(
                  size: Size(constraints.maxWidth, constraints.maxHeight),
                  painter: FaceGuideOverlayPainter(
                    guideColor: _currentGuideColor,
                    pulseValue: _pulseAnimation.value,
                    isRecording: widget.isRecording,
                  ),
                );
              },
            ),

            // Detection feedback (simplified)
            if (widget.currentDetection != null)
              _buildDetectionFeedback(constraints),

            // Instructions
            _buildInstructions(constraints),
          ],
        );
      },
    );
  }

  /// Builds detection feedback indicators
  Widget _buildDetectionFeedback(BoxConstraints constraints) {
    final detection = widget.currentDetection!;

    return Positioned(
      top: 100,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        decoration: BoxDecoration(
          color: _currentGuideColor.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _getFeedbackMessage(detection),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              'Coverage: '
              '${detection.coveragePercentage.toStringAsFixed(0)}%',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds instruction text
  Widget _buildInstructions(BoxConstraints constraints) {
    if (widget.isRecording) {
      return const SizedBox.shrink(); // Hide instructions during recording
    }

    return Positioned(
      bottom: 200,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Text(
          'Position your face within the oval guide.\n'
          'Keep your face centered and well-lit.',
          style: TextStyle(
            color: Colors.white,
            fontSize: 14,
            height: 1.4,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  /// Gets the guide color based on detection quality
  Color _getGuideColor(FaceDetectionResult? detection) {
    if (detection == null) {
      return Colors.white; // Default color when no detection
    }

    if (!detection.faceDetected) {
      return Colors.red; // No face detected
    }

    if (detection.faceCount > 1) {
      return Colors.orange; // Multiple faces detected
    }

    if (detection.meetsThreshold) {
      return Colors.green; // Good coverage
    }

    if (detection.coveragePercentage > 60) {
      return Colors.yellow; // Moderate coverage
    }

    return Colors.red; // Poor coverage
  }

  /// Gets feedback message based on detection result
  String _getFeedbackMessage(FaceDetectionResult detection) {
    if (!detection.faceDetected) {
      return 'No face detected';
    }

    if (detection.faceCount > 1) {
      return 'Multiple faces detected';
    }

    if (detection.meetsThreshold) {
      return 'Perfect! Keep this position';
    }

    if (detection.coveragePercentage > 60) {
      return 'Move closer to the camera';
    }

    return 'Position your face in the guide';
  }
}

/// {@template face_guide_overlay_painter}
/// Custom painter for the face guide overlay with cutout effect.
/// {@endtemplate}
class FaceGuideOverlayPainter extends CustomPainter {
  /// {@macro face_guide_overlay_painter}
  const FaceGuideOverlayPainter({
    required this.guideColor,
    required this.pulseValue,
    required this.isRecording,
  });

  /// Color of the guide border
  final Color guideColor;

  /// Pulse animation value (0.0 to 1.0)
  final double pulseValue;

  /// Whether recording is in progress
  final bool isRecording;

  @override
  void paint(Canvas canvas, Size size) {
    // Draw dark overlay
    final overlayPaint = Paint()..color = Colors.black.withValues(alpha: 0.5);
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), overlayPaint);

    // Create cutout for face guide
    final guideWidth = size.width * 0.7;
    final guideHeight = size.height * 0.5;
    final guideRect = Rect.fromCenter(
      center: Offset(size.width / 2, size.height / 2),
      width: guideWidth * pulseValue,
      height: guideHeight * pulseValue,
    );

    // Cut out the guide area
    final cutoutPaint = Paint()..blendMode = BlendMode.clear;
    canvas.drawOval(guideRect, cutoutPaint);

    // Draw guide border with optional glow effect
    final borderPaint = Paint()
      ..blendMode = BlendMode.srcOver
      ..style = PaintingStyle.stroke
      ..strokeWidth = isRecording ? 4.0 : 3.0
      ..color = guideColor;

    if (isRecording) {
      // Add subtle glow effect during recording
      borderPaint.maskFilter = const MaskFilter.blur(BlurStyle.outer, 2);
    }

    canvas.drawOval(guideRect, borderPaint);
  }

  @override
  bool shouldRepaint(covariant FaceGuideOverlayPainter oldDelegate) {
    return oldDelegate.guideColor != guideColor ||
        oldDelegate.pulseValue != pulseValue ||
        oldDelegate.isRecording != isRecording;
  }
}

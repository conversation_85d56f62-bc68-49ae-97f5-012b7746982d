import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/face_guide_overlay.dart';

/// {@template camera_preview_widget}
/// Widget that displays the camera preview with face detection overlay.
///
/// Shows a full-screen camera preview with a face guide overlay
/// and real-time face detection feedback.
/// {@endtemplate}
class CameraPreviewWidget extends StatelessWidget {
  /// {@macro camera_preview_widget}
  const CameraPreviewWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FaceVideoCaptureBloc, FaceVideoCaptureState>(
      builder: (context, state) {
        return Stack(
          children: [
            // Camera preview background
            _buildCameraPreview(context),

            // Face guide overlay
            FaceGuideOverlay(
              currentDetection: state.currentDetection,
              isRecording: state is Recording,
            ),
          ],
        );
      },
    );
  }

  /// Builds the camera preview
  Widget _buildCameraPreview(BuildContext context) {
    // TODO: Implement actual CamerAwesome integration
    // For now, show a clean placeholder that doesn't obstruct the face guide

    return Container(
      width: double.infinity,
      height: double.infinity,
      color: const Color(0xFF1A1A1A), // Clean dark background
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.camera_alt,
              size: 64,
              color: Colors.white24,
            ),
            SizedBox(height: 16),
            Text(
              'Camera Preview',
              style: TextStyle(
                color: Colors.white24,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'CamerAwesome integration pending',
              style: TextStyle(
                color: Colors.white12,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
